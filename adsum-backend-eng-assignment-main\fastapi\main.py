from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from database import get_db, Transaction
from pydantic import BaseModel
from typing import Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Financial Transactions API",
    description="API for accessing financial transaction data and insights",
    version="1.0.0"
)

# Pydantic models for API responses
class TransactionSummary(BaseModel):
    user_id: int
    total_transactions: int
    total_amount: float
    average_transaction_amount: float

class ErrorResponse(BaseModel):
    detail: str

@app.get("/")
async def root():
    return {"message": "Welcome to the Financial Transactions API"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Financial Transactions API"}

@app.get(
    "/transactions/{user_id}/summary",
    response_model=TransactionSummary,
    responses={
        404: {"model": ErrorResponse, "description": "No transactions found for user"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    }
)
async def get_user_transaction_summary(
    user_id: int,
    db: AsyncSession = Depends(get_db)
) -> TransactionSummary:
    """
    Get transaction summary for a specific user.

    Returns:
    - total_transactions: Number of transactions for the user
    - total_amount: Sum of all transaction amounts
    - average_transaction_amount: Average transaction amount
    """
    try:
        logger.info(f"Fetching transaction summary for user_id: {user_id}")

        # Query to get transaction statistics for the user
        query = select(
            func.count(Transaction.id).label('total_transactions'),
            func.sum(Transaction.amount).label('total_amount'),
            func.avg(Transaction.amount).label('average_amount')
        ).where(Transaction.user_id == user_id)

        result = await db.execute(query)
        stats = result.first()

        # Check if user has any transactions
        if stats.total_transactions == 0 or stats.total_transactions is None:
            logger.warning(f"No transactions found for user_id: {user_id}")
            raise HTTPException(
                status_code=404,
                detail=f"No transactions found for user_id: {user_id}"
            )

        # Handle potential None values and convert to appropriate types
        total_transactions = int(stats.total_transactions or 0)
        total_amount = float(stats.total_amount or 0.0)
        average_amount = float(stats.average_amount or 0.0)

        logger.info(f"Successfully retrieved summary for user_id: {user_id} - "
                   f"Transactions: {total_transactions}, Total: {total_amount:.2f}")

        return TransactionSummary(
            user_id=user_id,
            total_transactions=total_transactions,
            total_amount=round(total_amount, 2),
            average_transaction_amount=round(average_amount, 2)
        )

    except HTTPException:
        # Re-raise HTTP exceptions (like 404)
        raise
    except Exception as e:
        logger.error(f"Error fetching transaction summary for user_id {user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while fetching transaction summary"
        )

@app.get("/transactions/users")
async def get_users_with_transactions(
    limit: Optional[int] = 100,
    db: AsyncSession = Depends(get_db)
):
    """
    Get list of users who have transactions (for testing purposes).
    """
    try:
        query = select(Transaction.user_id).distinct().limit(limit)
        result = await db.execute(query)
        user_ids = [row[0] for row in result.fetchall()]

        return {
            "user_ids": user_ids,
            "count": len(user_ids),
            "message": "Use these user_ids to test the /transactions/{user_id}/summary endpoint"
        }

    except Exception as e:
        logger.error(f"Error fetching users: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while fetching users"
        )