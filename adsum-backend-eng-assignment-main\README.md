# Financial Data Engineering Assignment

This project implements a complete ETL pipeline and FastAPI service for processing financial transaction data at OpenTax.

## 🚀 Quick Start

### Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Git (to clone the repository)

### Setup Instructions

1. **<PERSON><PERSON> and navigate to the project:**

```bash
git clone <repository-url>
cd adsum-backend-eng-assignment-main
```

2. **Start all services:**

```bash
docker-compose up -d
```

3. **Wait for services to initialize** (about 2-3 minutes for first startup)

4. **Access the services:**
   - **Airflow UI**: <http://localhost:8080> (username: `admin`, password: `admin`)
   - **FastAPI Service**: <http://localhost:8000>
   - **API Documentation**: <http://localhost:8000/docs> (Swagger UI)
   - **PostgreSQL**: localhost:5432 (username: `airflow`, password: `airflow`)

## 📋 Implementation Overview

### Task A: ETL Pipeline

**Location**: `dags/etl_transactions.py`

**Features Implemented:**

- **Extract**: Loads financial transactions from CSV into Pandas DataFrame
- **Transform**:
  - Converts amounts to float (handles "$4990.00", empty values, negatives)
  - Normalizes dates to YYYY-MM-DD (handles MM/DD/YYYY, YYYY/MM/DD, YYYY-MM-DD formats)
  - Removes duplicate transactions based on transaction_id
  - Data quality validation and cleaning
- **Load**: Inserts cleaned data into PostgreSQL with conflict handling
- **Scheduling**: Runs daily at midnight (`0 0 * * *`)
- **Logging**: Comprehensive logging and error handling
- **Auditing**: Logs statistics for monitoring

**To Run the ETL:**

1. Go to Airflow UI: <http://localhost:8080>
2. Find the `etl_transactions` DAG
3. Toggle it ON and trigger manually or wait for scheduled run

### Task B: FastAPI Service

**Location**: `fastapi/main.py`

**Endpoints Implemented:**

#### 1. `GET /transactions/{user_id}/summary`

Returns transaction summary for a specific user:

```json
{
  "user_id": 123,
  "total_transactions": 50,
  "total_amount": 10240.50,
  "average_transaction_amount": 204.81
}
```

#### 2. `GET /transactions/users` (Helper endpoint)

Returns list of user IDs that have transactions (for testing)

#### 3. `GET /health` (Health check)

Service health status

**Features:**

- ✅ Async/await implementation for efficiency
- ✅ SQLAlchemy with PostgreSQL integration
- ✅ Proper error handling (404 for no transactions, 500 for server errors)
- ✅ Comprehensive logging
- ✅ Pydantic models for request/response validation
- ✅ OpenAPI documentation (Swagger)

**Testing the API:**

```bash
# Get list of users with transactions
curl http://localhost:8000/transactions/users

# Get summary for a specific user (replace 1408 with actual user_id)
curl http://localhost:8000/transactions/1408/summary

# Or use the interactive docs at http://localhost:8000/docs
```

### Task C: Database Query Optimization

**Location**: `DATABASE_OPTIMIZATION.md`

**Comprehensive analysis covering:**

- Current indexing strategy and recommendations
- Query performance optimization techniques
- Materialized views for heavy aggregations
- Partitioning strategies for large datasets
- Caching with Redis
- ETL pipeline optimizations
- Monitoring and maintenance procedures
- Performance benchmarks and scaling considerations

## 🗄️ Database Schema

```sql
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    amount FLOAT NOT NULL,
    transaction_date DATE NOT NULL
);

-- Optimized indexes
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
```

## 📊 Sample Data

The dataset (`data/financial_transactions.csv`) contains 1,016 financial transactions with:

- Various amount formats: `$4990.00`, `-3437.55`, `906.17`, empty values
- Multiple date formats: `2024-11-07`, `10/11/2024`, `2024/06/30`
- Duplicate transactions for testing deduplication
- Missing values for testing data quality handling

## 🔧 Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Apache        │    │   PostgreSQL    │    │   FastAPI       │
│   Airflow       │───▶│   Database      │◀───│   Service       │
│   (ETL)         │    │                 │    │   (API)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
   Port 8080              Port 5432               Port 8000
```

## 🧪 Testing

### Manual Testing Steps

1. **Test ETL Pipeline:**
   - Access Airflow UI at <http://localhost:8080>
   - Trigger the `etl_transactions` DAG
   - Monitor logs for successful execution
   - Check database for loaded data

2. **Test FastAPI Service:**
   - Visit <http://localhost:8000/docs> for interactive testing
   - Use `/transactions/users` to get valid user IDs
   - Test `/transactions/{user_id}/summary` with valid user IDs
   - Test error handling with non-existent user ID

3. **Database Verification:**

```bash
# Connect to PostgreSQL
docker exec -it <postgres_container_id> psql -U airflow -d finance_app

# Check loaded data
SELECT COUNT(*) FROM transactions;
SELECT user_id, COUNT(*), SUM(amount) FROM transactions GROUP BY user_id LIMIT 10;
```

## 📈 Performance Considerations

- **Current Performance**: Sub-second response times for user summary queries
- **Scalability**: Designed to handle millions of transactions
- **Optimization**: Comprehensive indexing and query optimization strategies
- **Monitoring**: Built-in logging and error tracking

## 🛠️ Development

### Project Structure

```
├── dags/
│   └── etl_transactions.py      # Airflow ETL pipeline
├── fastapi/
│   ├── main.py                  # FastAPI application
│   ├── database.py              # Database models and connection
│   ├── requirements.txt         # Python dependencies
│   └── Dockerfile              # FastAPI container config
├── data/
│   └── financial_transactions.csv  # Sample dataset
├── docker-compose.yml           # Multi-service orchestration
├── init-db.sql                 # Database initialization
├── DATABASE_OPTIMIZATION.md    # Performance analysis
└── README.md                   # This file
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting:**

   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

2. **Database connection issues:**
   - Wait 2-3 minutes for PostgreSQL to fully initialize
   - Check logs: `docker-compose logs postgres`

3. **Airflow DAG not appearing:**
   - Check logs: `docker-compose logs webserver`
   - Ensure DAG file syntax is correct

4. **API returning 500 errors:**
   - Ensure ETL has run successfully and data exists
   - Check FastAPI logs: `docker-compose logs fastapi`
